import React, { useState, useEffect } from "react";
import { FaTimes } from "react-icons/fa";
import "../../styles/BidModal.css";

const BidModal = ({ isOpen, onClose, strategy }) => {
  const [bidAmount, setBidAmount] = useState("");
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 1,
    minutes: 59,
    seconds: 30
  });

  // Mock bid history data
  const bidHistory = [
    { no: 1, bidId: "#2024/05", date: "20 May 2024 | 4:50PM", bidAmount: "$20.00" },
    { no: 2, bidId: "#2024/04", date: "20 May 2024 | 4:45PM", bidAmount: "$22.00" },
    { no: 3, bidId: "#2024/03", date: "20 May 2024 | 4:30PM", bidAmount: "$22.00" },
    { no: 4, bidId: "#2024/02", date: "20 May 2024 | 4:30PM", bidAmount: "$22.00" },
    { no: 5, bidId: "#2024/01", date: "20 May 2024 | 4:30PM", bidAmount: "$22.00" }
  ];

  // Countdown timer effect
  useEffect(() => {
    if (!isOpen) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
        } else if (prev.days > 0) {
          return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 };
        }
        return prev;
      });
    }, 1000); // Update every second

    return () => clearInterval(timer);
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleSubmitBid = (e) => {
    e.preventDefault();
    if (bidAmount.trim()) {
      // Handle bid submission logic here
      console.log("Bid submitted:", bidAmount);
      setBidAmount("");
      onClose();
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="bid-modal-overlay" onClick={handleOverlayClick}>
      <div className="bid-modal">
        <div className="bid-modal__header">
          <h2 className="bid-modal__title">Bid is Available Now!</h2>
          <button className="bid-modal__close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="bid-modal__content">
          {/* Countdown Timer */}
          <div className="bid-modal__countdown">
            <div className="countdown-item">
              <span className="countdown-number">{String(timeLeft.days).padStart(2, '0')}</span>
              <span className="countdown-label">DAYS</span>
            </div>
            <div className="countdown-item">
              <span className="countdown-number">{String(timeLeft.hours).padStart(2, '0')}</span>
              <span className="countdown-label">HOURS</span>
            </div>
            <div className="countdown-item">
              <span className="countdown-number">{String(timeLeft.minutes).padStart(2, '0')}</span>
              <span className="countdown-label">MINUTES</span>
            </div>
            <div className="countdown-item">
              <span className="countdown-number">{String(timeLeft.seconds).padStart(2, '0')}</span>
              <span className="countdown-label">SECONDS</span>
            </div>
          </div>

          {/* Bid Form */}
          <div className="bid-modal__form-section">
            <h3 className="bid-modal__form-title">Enter Your Bid Amount</h3>
            <form onSubmit={handleSubmitBid} className="bid-modal__form">
              <input
                type="number"
                step="0.01"
                min="0"
                value={bidAmount}
                onChange={(e) => setBidAmount(e.target.value)}
                placeholder="Enter Bid Amount"
                className="bid-modal__input"
                required
              />
              <button type="submit" className="bid-modal__submit-btn">
                SUBMIT BID
              </button>
            </form>
          </div>

          {/* Others Bid Section */}
          <div className="bid-modal__history-section">
            <h3 className="bid-modal__history-title">Others Bid</h3>
            <div className="bid-modal__table-container">
              <table className="bid-modal__table">
                <thead>
                  <tr>
                    <th>No.</th>
                    <th>Bid Id</th>
                    <th>Date</th>
                    <th>Bid Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {bidHistory.map((bid) => (
                    <tr key={bid.no}>
                      <td>{bid.no}</td>
                      <td>{bid.bidId}</td>
                      <td>{bid.date}</td>
                      <td>{bid.bidAmount}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BidModal;
