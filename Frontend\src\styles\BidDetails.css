/* BidDetails Component Styles */
.BidDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

.BidDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Header Section */
.BidDetails__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading5) 0;
  margin-bottom: var(--heading4);
}

.BidDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.BidDetails__content-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.BidDetails__content-details {
  flex: 1;
}

.BidDetails__content-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.4;
}

.BidDetails__content-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin: 0;
}

/* Main Section */
.BidDetails__main-section {
  padding: var(--heading5);
}

.BidDetails__info-grid {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--heading4);
  margin-top: var(--heading4);
}

.BidDetails__info-section {
  display: flex;
  justify-content: space-around;
  gap: var(--basefont);
}

.BidDetails__section-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
}

.vertical-line {
  width: 1px;
  background-color: var(--light-gray);
  height: 100%;
  margin: 0 20px;
}

.BidDetails__info-item-grid {
  width: 40%;
}

.bidDetails-btn-grid {
  display: flex;
  align-items: center;
}

.BidDetails__info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid #f5f5f5;
}

.BidDetails__info-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

.BidDetails__info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Action Buttons */
.BidDetails__actions {
  padding: 0 0 0 var(--heading4);
  display: flex;
  flex-wrap: wrap;
  gap: var(--smallfont);
}

.BidDetails__btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.BidDetails__btn--accept {
  background-color: #28a745;
  color: white;
}

.BidDetails__btn--accept:hover {
  background-color: #218838;
}

.BidDetails__btn--reject {
  background-color: #dc3545;
  color: white;
}

.BidDetails__btn--reject:hover {
  background-color: #c82333;
}

/* History Section */
.BidDetails__history-section {
  background-color: var(--white);
  padding: var(--heading5);
}

.BidDetails__history-table {
  margin-top: var(--basefont);
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  text-align: center;
  min-width: 80px;
  display: inline-block;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-accepted {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-counter-offer {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Error State */
.BidDetails__error {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--heading3);
  color: var(--dark-gray);
  font-size: var(--heading6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .BidDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .BidDetails__info-grid {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }

  .BidDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .BidDetails__content-image {
    width: 100%;
    height: 120px;
  }

  .BidDetails__actions {
    flex-direction: column;
  }

  .BidDetails__btn {
    width: 100%;
    text-align: center;
  }

  .BidDetails__main-section,
  .BidDetails__history-section {
    padding: var(--basefont);
  }

  .BidDetails__section-title {
    font-size: var(--basefont);
  }

  .BidDetails__content-title {
    font-size: var(--basefont);
  }

  .BidDetails__content-subtitle,
  .BidDetails__info-label,
  .BidDetails__info-value {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .BidDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .BidDetails__main-section,
  .BidDetails__history-section {
    padding: var(--smallfont);
  }

  .BidDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .BidDetails__content-title {
    font-size: var(--smallfont);
  }

  .BidDetails__content-subtitle,
  .BidDetails__info-label,
  .BidDetails__info-value {
    font-size: var(--extrasmallfont);
  }

  .BidDetails__btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .status-badge {
    font-size: var(--extrasmallfont);
    padding: 2px 6px;
    min-width: 60px;
  }
}

@media (max-width: 400px) {
  .BidDetails__info-section {
    flex-direction: column;
    gap: 0px;
  }
  
  .BidDetails__section-title {
    margin-top: var(--smallfont);
    margin-bottom: 0px;
  }
}
